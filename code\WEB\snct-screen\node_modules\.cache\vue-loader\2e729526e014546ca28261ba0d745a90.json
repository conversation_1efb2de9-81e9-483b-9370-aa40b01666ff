{"remainingRequest": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\src\\views\\indexs\\left-company-list.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\src\\views\\indexs\\left-company-list.vue", "mtime": 1753840482865}, {"path": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753075295560}, {"path": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753075297856}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CmltcG9ydCB7IGRhdGFNb2R1bGUgfSBmcm9tICdAL3V0aWxzL3dlYlNvY2tldCcKCmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAnTGVmdENvbXBhbnlMaXN0JywKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgY29tcGFueUxpc3Q6IFtdLAogICAgICBwYWdlZmxhZzogdHJ1ZSwKICAgICAgdGltZXI6IG51bGwsCiAgICAgIHdzQ2hlY2tUaW1lcjogbnVsbAogICAgfQogIH0sCiAgY3JlYXRlZCgpIHsKICAgIHRoaXMuc3RhcnREYXRhTW9uaXRvcmluZygpCiAgfSwKICBtb3VudGVkKCkgewogICAgdGhpcy5jaGVja1dlYlNvY2tldERhdGEoKQogIH0sCiAgYmVmb3JlRGVzdHJveSgpIHsKICAgIHRoaXMuY2xlYXJUaW1lcnMoKQogIH0sCiAgbWV0aG9kczogewogICAgc3RhcnREYXRhTW9uaXRvcmluZygpIHsKICAgICAgdGhpcy53c0NoZWNrVGltZXIgPSBzZXRJbnRlcnZhbCgoKSA9PiB7CiAgICAgICAgdGhpcy5jaGVja1dlYlNvY2tldERhdGEoKQogICAgICB9LCAxMDAwKQogICAgfSwKICAgIAogICAgLy8g5qOA5p+lV2ViU29ja2V05pWw5o2uCiAgICBjaGVja1dlYlNvY2tldERhdGEoKSB7CiAgICAgIGlmIChkYXRhTW9kdWxlLkQwQTAxICYmIEFycmF5LmlzQXJyYXkoZGF0YU1vZHVsZS5EMEEwMSkpIHsKICAgICAgICBjb25zdCBuZXdEYXRhID0gWy4uLmRhdGFNb2R1bGUuRDBBMDFdCiAgICAgICAgaWYgKEpTT04uc3RyaW5naWZ5KG5ld0RhdGEpICE9PSBKU09OLnN0cmluZ2lmeSh0aGlzLmNvbXBhbnlMaXN0KSkgewogICAgICAgICAgdGhpcy5jb21wYW55TGlzdCA9IG5ld0RhdGEKICAgICAgICAgIGNvbnNvbGUubG9nKCfkvIHkuJrliJfooajmlbDmja7mm7TmlrA6JywgdGhpcy5jb21wYW55TGlzdCkKICAgICAgICB9CiAgICAgIH0KICAgIH0sCiAgICAKICAgIC8vIOWkhOeQhumHjeaWsOi/nuaOpeivt+axggogICAgaGFuZGxlUmVjb25uZWN0KCkgewogICAgICAvLyDpgJrnn6XniLbnu4Tku7bph43mlrDliJ3lp4vljJZXZWJTb2NrZXTov57mjqUKICAgICAgdGhpcy4kZW1pdCgncmVjb25uZWN0JykKICAgICAgdGhpcy5wYWdlZmxhZyA9IHRydWUKICAgIH0sCgogICAgLy8g5aSE55CG5LyB5Lia54K55Ye75LqL5Lu2CiAgICBoYW5kbGVDb21wYW55Q2xpY2soY29tcGFueSkgewogICAgICBjb25zb2xlLmxvZygn54K55Ye75LyB5LiaOicsIGNvbXBhbnkpCgogICAgICAvLyDkv67mlLlkYXRhTW9kdWxlLnNlbmR0ZXh0CiAgICAgIGRhdGFNb2R1bGUuc2VuZHRleHQgPSBgdHlwZTY2IyR7Y29tcGFueS5kZXB0SWR9IzAjMEIwMSwwQjAyLDBCMDMsMEIwNCwwQjA1LDBCMDZgCgogICAgICAvLyDlsIbkvIHkuJrkv6Hmga/kv53lrZjliLBzZXNzaW9uU3RvcmFnZe+8iOWFs+mXrea1j+iniOWZqOWQjua4hemZpO+8iQogICAgICBjb25zdCBjb21wYW55SW5mbyA9IHsKICAgICAgICBkZXB0SWQ6IGNvbXBhbnkuZGVwdElkLAogICAgICAgIHNlbmR0ZXh0OiBkYXRhTW9kdWxlLnNlbmR0ZXh0LAogICAgICAgIHRpdGxlOiBjb21wYW55LnRpdGxlLAogICAgICAgIHRpbWVzdGFtcDogRGF0ZS5ub3coKSwKICAgICAgICBmcm9tUGFnZTogJ2NvbXBhbnktbGlzdCcKICAgICAgfQogICAgICBzZXNzaW9uU3RvcmFnZS5zZXRJdGVtKCdzZWxlY3RlZENvbXBhbnknLCBKU09OLnN0cmluZ2lmeShjb21wYW55SW5mbykpCgogICAgICBjb25zb2xlLmxvZygn5pu05paw5ZCO55qEc2VuZHRleHQ6JywgZGF0YU1vZHVsZS5zZW5kdGV4dCkKCiAgICAgIC8vIOi3s+i9rOWIsOS8geS4mummlumhtQogICAgICB0aGlzLiRyb3V0ZXIucHVzaCgnL2NvbWluZGV4JykKICAgIH0sCgogICAgLy8g5riF55CG5a6a5pe25ZmoCiAgICBjbGVhclRpbWVycygpIHsKICAgICAgaWYgKHRoaXMudGltZXIpIHsKICAgICAgICBjbGVhckludGVydmFsKHRoaXMudGltZXIpCiAgICAgICAgdGhpcy50aW1lciA9IG51bGwKICAgICAgfQogICAgICBpZiAodGhpcy53c0NoZWNrVGltZXIpIHsKICAgICAgICBjbGVhckludGVydmFsKHRoaXMud3NDaGVja1RpbWVyKQogICAgICAgIHRoaXMud3NDaGVja1RpbWVyID0gbnVsbAogICAgICB9CiAgICB9CiAgfQp9Cg=="}, {"version": 3, "sources": ["left-company-list.vue"], "names": [], "mappings": ";AAwCA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "left-company-list.vue", "sourceRoot": "src/views/indexs", "sourcesContent": ["<!--\n * @Author: weizw\n * @Date: 2025-07-28\n * @Description: 企业列表组件\n-->\n<template>\n  <div class=\"company-list-container\" v-if=\"pageflag\">\n    <div class=\"company-list beautify-scroll-def\">\n      <div\n        v-for=\"(company, index) in companyList\"\n        :key=\"company.deptId\"\n        class=\"company-item\"\n        :class=\"{ 'active': company.status === '1', 'inactive': company.status === '0' }\"\n        @click=\"handleCompanyClick(company)\"\n      >\n        <div class=\"company-index\">{{ index + 1 }}</div>\n        <div class=\"company-info\">\n          <div class=\"company-name\">{{ company.deptName }}</div>\n          <div class=\"company-status\">\n            <!-- <span class=\"status-dot\" :class=\"company.status === '1' ? 'online' : 'offline'\"></span> -->\n            <!-- <span class=\"status-text\">{{ company.status === '1' ? '在线' : '离线' }}</span> -->\n          </div>\n        </div>\n        <!-- <div class=\"company-id\">ID: {{ company.deptId }}</div> -->\n      </div>\n    </div>\n    \n    <!-- 无数据时显示 -->\n    <div v-if=\"companyList.length === 0\" class=\"no-data\">\n      <div class=\"no-data-text\">暂无企业数据</div>\n    </div>\n  </div>\n  \n  <!-- 数据加载失败时显示重新获取按钮 -->\n  <Reacquire v-else @onclick=\"handleReconnect\" line-height=\"200px\">\n    重新获取\n  </Reacquire>\n</template>\n\n<script>\nimport { dataModule } from '@/utils/webSocket'\n\nexport default {\n  name: 'LeftCompanyList',\n  data() {\n    return {\n      companyList: [],\n      pageflag: true,\n      timer: null,\n      wsCheckTimer: null\n    }\n  },\n  created() {\n    this.startDataMonitoring()\n  },\n  mounted() {\n    this.checkWebSocketData()\n  },\n  beforeDestroy() {\n    this.clearTimers()\n  },\n  methods: {\n    startDataMonitoring() {\n      this.wsCheckTimer = setInterval(() => {\n        this.checkWebSocketData()\n      }, 1000)\n    },\n    \n    // 检查WebSocket数据\n    checkWebSocketData() {\n      if (dataModule.D0A01 && Array.isArray(dataModule.D0A01)) {\n        const newData = [...dataModule.D0A01]\n        if (JSON.stringify(newData) !== JSON.stringify(this.companyList)) {\n          this.companyList = newData\n          console.log('企业列表数据更新:', this.companyList)\n        }\n      }\n    },\n    \n    // 处理重新连接请求\n    handleReconnect() {\n      // 通知父组件重新初始化WebSocket连接\n      this.$emit('reconnect')\n      this.pageflag = true\n    },\n\n    // 处理企业点击事件\n    handleCompanyClick(company) {\n      console.log('点击企业:', company)\n\n      // 修改dataModule.sendtext\n      dataModule.sendtext = `type66#${company.deptId}#0#0B01,0B02,0B03,0B04,0B05,0B06`\n\n      // 将企业信息保存到sessionStorage（关闭浏览器后清除）\n      const companyInfo = {\n        deptId: company.deptId,\n        sendtext: dataModule.sendtext,\n        title: company.title,\n        timestamp: Date.now(),\n        fromPage: 'company-list'\n      }\n      sessionStorage.setItem('selectedCompany', JSON.stringify(companyInfo))\n\n      console.log('更新后的sendtext:', dataModule.sendtext)\n\n      // 跳转到企业首页\n      this.$router.push('/comindex')\n    },\n\n    // 清理定时器\n    clearTimers() {\n      if (this.timer) {\n        clearInterval(this.timer)\n        this.timer = null\n      }\n      if (this.wsCheckTimer) {\n        clearInterval(this.wsCheckTimer)\n        this.wsCheckTimer = null\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.company-list-container {\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n}\n\n.company-list {\n  flex: 1;\n  overflow-y: auto;\n  padding: 10px 0;\n  \n  .company-item {\n    display: flex;\n    align-items: center;\n    padding: 12px 16px;\n    margin: 8px 2px;\n    border-radius: 6px;\n    background: linear-gradient(135deg, rgba(0, 123, 255, 0.1), rgba(0, 234, 255, 0.05));\n    border: 1px solid rgba(0, 234, 255, 0.2);\n    transition: all 0.3s ease;\n    cursor: pointer;\n\n    &:hover {\n      background: linear-gradient(135deg, rgba(0, 123, 255, 0.2), rgba(0, 234, 255, 0.1));\n      border-color: rgba(0, 234, 255, 0.4);\n      transform: translateX(2px);\n      box-shadow: 0 4px 12px rgba(0, 234, 255, 0.3);\n    }\n    \n    &.active {\n      border-color: rgba(7, 247, 168, 0.4);\n      background: linear-gradient(135deg, rgba(7, 247, 168, 0.1), rgba(7, 247, 168, 0.05));\n    }\n    \n    &.inactive {\n      border-color: rgba(227, 179, 55, 0.4);\n      background: linear-gradient(135deg, rgba(227, 179, 55, 0.1), rgba(227, 179, 55, 0.05));\n    }\n  }\n  \n  .company-index {\n    width: 30px;\n    height: 30px;\n    border-radius: 50%;\n    background: linear-gradient(135deg, #0072ff, #00eaff);\n    color: white;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    font-size: 14px;\n    font-weight: bold;\n    margin-right: 12px;\n    flex-shrink: 0;\n  }\n  \n  .company-info {\n    flex: 1;\n    min-width: 0;\n    \n    .company-name {\n      color: #ffffff;\n      font-size: 16px;\n      font-weight: 500;\n      margin-bottom: 4px;\n      white-space: nowrap;\n      overflow: hidden;\n      text-overflow: ellipsis;\n    }\n    \n    .company-status {\n      display: flex;\n      align-items: center;\n      \n      .status-dot {\n        width: 8px;\n        height: 8px;\n        border-radius: 50%;\n        margin-right: 6px;\n        \n        &.online {\n          background-color: #07f7a8;\n          box-shadow: 0 0 6px rgba(7, 247, 168, 0.6);\n        }\n        \n        &.offline {\n          background-color: #e3b337;\n          box-shadow: 0 0 6px rgba(227, 179, 55, 0.6);\n        }\n      }\n      \n      .status-text {\n        color: rgba(255, 255, 255, 0.8);\n        font-size: 12px;\n      }\n    }\n  }\n  \n  .company-id {\n    color: rgba(255, 255, 255, 0.6);\n    font-size: 12px;\n    flex-shrink: 0;\n  }\n}\n\n.no-data {\n  flex: 1;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  \n  .no-data-text {\n    color: rgba(255, 255, 255, 0.6);\n    font-size: 16px;\n  }\n}\n\n// 滚动条样式\n.beautify-scroll-def {\n  &::-webkit-scrollbar {\n    width: 6px;\n  }\n  \n  &::-webkit-scrollbar-track {\n    background: rgba(255, 255, 255, 0.1);\n    border-radius: 3px;\n  }\n  \n  &::-webkit-scrollbar-thumb {\n    background: rgba(0, 234, 255, 0.4);\n    border-radius: 3px;\n    \n    &:hover {\n      background: rgba(0, 234, 255, 0.6);\n    }\n  }\n}\n</style>\n"]}]}