import { removeToken } from '@/utils/auth'

var ws = null;
var connectionStatus = "未连接";
var reconnectAttempts = 0;
var maxReconnectAttempts = 86400*30;
var reconnectInterval = null;
var isManualClose = false; // 标记是否手动关闭

export const dataModule = {
    title:'智慧船情可视化平台',
    sendtext:'',
    D0A01:'',
    D0A02:'',
    D0A03:'',
    D0A04:'',
    D0B01:'',
    D0B02:'',
    D0B03:'',
    D0B04:'',
    D0B05:'',
    D0B06:'',
    D0D01:'',
    D0D02:'',
    D0C01:'',
    D0C02:'',
    D0E01:'',
    D0E02:'',
    D0E03:''
};

/*
 * zhoufd
 * WebSocket 工具
 */
export function initWebSocket() {
    // 清除之前的连接
    if (ws!=null) {
      ws.close();
    }
    const token = localStorage.getItem('Admin-Token')
    if(token==null){
      //location.reload();
      this.$message.error('登录失败：未获取到token')
    }
    // 创建 WebSocket 连接 (替换为你的 WS 地址)
    ws = new WebSocket("ws://127.0.0.1:8090/snct-visual/websocket/"+token);

    // 连接成功
    ws.onopen = () => {
      isManualClose = false;  //开启断开后自动重连
      connectionStatus = "已连接";
      reconnectAttempts = 0; // 重置重连次数
      console.log("WebSocket 连接成功");
      //sendtext = "type66#101#0#0A02,0A03,0A01,0A04";
      //this.sendtext = "type66#101#0#0"; 
      //格式：type66#deptId#sn#codes） type66固定表示请求数据包   codes可以多个，逗号隔开 , 参数必填，没有值用0 代替
      sendMessage(dataModule.sendtext);
    };

    // 接收消息
    ws.onmessage = (event) => {
        console.log("收到后端消息0:", event.data);
        var json = JSON.parse(event.data);
      if(json.code=='0A01'){
        dataModule.D0A01 = json.data
        //console.log("收到后端消息1:", dataModule.D0A01);
      }
      if(json.code=='0A02'){
        dataModule.D0A02 = json.data
        //console.log("收到后端消息1:", dataModule.D0A01);
      }
      if(json.code=='0A03'){
        dataModule.D0A03 = json.data
        //console.log("收到后端消息1:", dataModule.D0A01);
      }
      if(json.code=='0A04'){
        dataModule.D0A04 = json.data
        //console.log("收到后端消息1:", dataModule.D0A01);
      }
      if(json.code=='0B01'){
        dataModule.D0B01 = json.data
        //console.log("收到后端消息1:", dataModule.D0A01);
      }
      if(json.code=='0B02'){
        dataModule.D0B02 = json.data
        //console.log("收到后端消息1:", dataModule.D0A01);
      }
      if(json.code=='0B03'){
        dataModule.D0B03 = json.data
        //console.log("收到后端消息1:", dataModule.D0A01);
      }
      if(json.code=='0B04'){
        dataModule.D0B04 = json.data
        //console.log("收到后端消息1:", dataModule.D0A01);
      }
      if(json.code=='0B05'){
        dataModule.D0B05 = json.data
        //console.log("收到后端消息1:", dataModule.D0A01);
      }
      if(json.code=='0B06'){
        dataModule.D0B06 = json.data
        //console.log("收到后端消息1:", dataModule.D0A01);
      }
      if(json.code=='0C01'){
        dataModule.D0C01 = json.data
        //console.log("收到后端消息1:", dataModule.D0A01);
      }
      if(json.code=='0C02'){
        dataModule.D0C02 = json.data
        //console.log("收到后端消息1:", dataModule.D0A01);
      }
      if(json.code=='0D01'){
        dataModule.D0D01 = json.data
        //console.log("收到后端消息1:", dataModule.D0A01);
      }
      if(json.code=='0D02'){
        dataModule.D0D02 = json.data
        //console.log("收到后端消息1:", dataModule.D0A01);
      }
      if(json.code=='0E01'){
        dataModule.D0E01 = json.data
        //console.log("收到后端消息1:", dataModule.D0A01);
      }
      if(json.code=='0E02'){
        dataModule.D0E02 = json.data
        //console.log("收到后端消息1:", dataModule.D0A01);
      }
      if(json.code=='0E03'){
        dataModule.D0E03 = json.data
        //console.log("收到后端消息1:", dataModule.D0A01);
      }
      if(json.code=='0009'){
        //凭证验证不通过;
        removeToken()
        localStorage.removeItem('settingData')
        //关闭长连接
        manualClose();
        //alert('鉴权失败，请确认账号是否有足够权限');
        location.reload();
      }
    };

    // 连接关闭
    ws.onclose = (event) => {
      connectionStatus = "已断开";
      
      if (!isManualClose) {
        console.warn(`连接关闭，代码: ${event.code}, 原因: ${event.reason}`);
        attemptReconnect();
      }
    };

    // 连接错误
    ws.onerror = (error) => {
      console.error("WebSocket 错误:", error);
      ws.close(); // 触发 onclose 进行重连
    };
  }

      // 尝试重新连接（指数退避算法）
export function attemptReconnect() {
    
    if (reconnectAttempts >= maxReconnectAttempts) {
      console.error(`已达到最大重连次数 (${maxReconnectAttempts})，停止重连`);
      return;
    }
    // 计算重连延迟（指数增长：1s, 2s, 4s, 8s...）
    const delay = Math.min(3000 * Math.pow(2, reconnectAttempts), 8000);
    
    console.log(`将在 ${delay} 毫秒后尝试第 ${reconnectAttempts + 1} 次重连...`);
    
    reconnectInterval = setTimeout(() => {
      reconnectAttempts++;
      initWebSocket();
    }, delay);
  }

export function sendMessage(message) {
    if (ws && ws.readyState === WebSocket.OPEN) {
      //const message = `测试消息 ${new Date().toLocaleTimeString()}`;
      const token = localStorage.getItem('Admin-Token')
      if(token==null){
        //location.reload();
      }
      ws.send(message);
      console.log("向服务器发送消息:", message);
    } else {
      console.warn("无法发送消息，WebSocket 未连接");
    }
  }

  // 手动关闭连接（不会触发重连）
export function manualClose() {
    isManualClose = true;
    if (ws!=null) {
      ws.close();
    }
    setTimeout(function() {
      isManualClose = false;
    }, 16000); // 16000毫秒后执行恢复断开重连
  }

  // 清空数据模块中的所有数据
export function clearData() {
    dataModule.sendtext = '';
    dataModule.D0A01 = '';
    dataModule.D0A02 = '';
    dataModule.D0A03 = '';
    dataModule.D0A04 = '';
    dataModule.D0B01 = '';
    dataModule.D0B02 = '';
    dataModule.D0B03 = '';
    dataModule.D0B04 = '';
    dataModule.D0B05 = '';
    dataModule.D0B06 = '';
    dataModule.D0D01 = '';
    dataModule.D0D02 = '';
    dataModule.D0C01 = '';
    dataModule.D0C02 = '';
    dataModule.D0E01 = '';
    dataModule.D0E02 = '';
    dataModule.D0E03 = '';
    console.log("数据已清空");
  }




