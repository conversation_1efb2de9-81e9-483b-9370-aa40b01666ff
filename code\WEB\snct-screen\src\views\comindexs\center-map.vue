<!--
 * @Author: daidai
 * @Date: 2022-03-01 11:17:39
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2022-09-29 15:50:18
 * @FilePath: \web-pc\src\pages\big-screen\view\indexs\center-map.vue
-->
<template>
  <div class="centermap">
    <div  style="margin-top: 15px;">
      <button class="tech-button" :class="{ 'active': isActive1 }" @click="toggleActive1">
        <span class="text">首页</span>
      </button>
      <button class="tech-button" :class="{ 'active': isActive2 }" @click="toggleActive2">
        <span class="text">实时监控</span>
      </button>
      <button class="tech-button" :class="{ 'active': isActive3 }" @click="toggleActive3">
        <span class="text">实时轨迹</span>
      </button>
      <button class="tech-button" :class="{ 'active': isActive4 }" @click="toggleActive4">
        <span class="text">平台信息</span>
      </button>
    </div>
    <div style="width: 60%;float: right;margin-right: 16px;" class="maptitle">
      <div class="zuo"></div>
        <span class="titletext">{{ maptitle }}</span>
      <div class="you"></div>
    </div>
    <div class="mapwrap">
      <dv-border-box-13>
        <div class="quanguo" @click="getData('china')" v-if="code !== 'china'">
          中国
        </div>

        <Echart id="CenterMap" :options="options" ref="CenterMap" />
      </dv-border-box-13>
    </div>
  </div>
</template>

<script>
import xzqCode from "../../utils/map/xzqCode";
import { currentGET } from "api/modules";
import * as echarts from "echarts";
import { GETNOBASE } from "api";
export default {
  data() {
    return {
      maptitle: "接入船总数量：",
      options: {},
      code: "china", //china 代表中国 其他地市是行政编码
      echartBindClick: false,
      isSouthChinaSea: false, //是否要展示南海群岛  修改此值请刷新页面
      isActive1: true,
      isActive2: false,
      isActive3: false,
      isActive4: false
    };
  },
  created() {},

  mounted() {
    // console.log(xzqCode);
    //this.getData("china");
  },
  methods: {
    getData(code) {

    },
    message(text) {
      this.$Message({
        text: text,
        type: "warning",
      });
    },
    toggleActive1() {
      this.isActive1 = true
      this.isActive2 = false
      this.isActive3 = false
      this.isActive4 = false
    },
    toggleActive2() {
      this.isActive1 = false
      this.isActive2 = true
      this.isActive3 = false
      this.isActive4 = false
    },
    toggleActive3() {
      this.isActive1 = false
      this.isActive2 = false
      this.isActive3 = true
      this.isActive4 = false
    },
    toggleActive4() {
      this.isActive1 = false
      this.isActive2 = false
      this.isActive3 = false
      this.isActive4 = true
    }
  },
};
</script>
<style lang="scss" scoped>
.centermap {
  .maptitle {
    height: 40px;
    display: flex;
    justify-content: right;
    box-sizing: border-box;
    margin: 35px 10px 0 0;

    .titletext {
      font-size: 19px;
      font-weight: 300;
      letter-spacing: 1px;
      background: linear-gradient(
        92deg,
        #0072ff 0%,
        #00eaff 48.8525390625%,
        #01aaff 100%
      );
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      margin: 0 16px;
    }

    .zuo,
    .you {
      background-size: 100% 100%;
      width: 26px;
      height: 14px;
      margin-top: 2px;
    }

    .zuo {
      background: url("../../assets/img/xiezuo.png") no-repeat;
    }

    .you {
      background: url("../../assets/img/xieyou.png") no-repeat;
    }
  }

  .mapwrap {
    margin-top: 10px;
    height: 890px;
    width: 100%;
    box-sizing: border-box;
    position: relative;

    .quanguo {
      position: absolute;
      right: 20px;
      top: -46px;
      width: 80px;
      height: 28px;
      border: 1px solid #00eded;
      border-radius: 10px;
      color: #00f7f6;
      text-align: center;
      line-height: 26px;
      letter-spacing: 6px;
      cursor: pointer;
      box-shadow: 0 2px 4px rgba(0, 237, 237, 0.5),
        0 0 6px rgba(0, 237, 237, 0.4);
    }
  }

.tech-button {
  position: relative;
  padding: 12px 24px;
  border: none;
  background: linear-gradient(45deg, #101011, #15323f);
  color: white;
  font-size: 16px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(0,0,0,0.2);
  overflow: hidden;
}

.tech-button.active {
  background: linear-gradient(45deg, #1a2e3d, #b9cedf);
  box-shadow: 0 8px 30px rgba(0,0,0,0.4);
}

}

</style>
