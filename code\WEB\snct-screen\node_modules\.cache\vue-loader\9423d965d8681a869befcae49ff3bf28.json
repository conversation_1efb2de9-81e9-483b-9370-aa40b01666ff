{"remainingRequest": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\src\\views\\indexs\\center-map.vue?vue&type=style&index=0&id=10948d28&lang=scss&scoped=true&", "dependencies": [{"path": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\src\\views\\indexs\\center-map.vue", "mtime": 1753841504999}, {"path": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1753075296083}, {"path": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1753075298360}, {"path": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1753075296703}, {"path": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1753075295543}, {"path": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753075295560}, {"path": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753075297856}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQouY2VudGVybWFwIHsNCiAgLm1hcHRpdGxlIHsNCiAgICBkaXNwbGF5OiBmbGV4Ow0KICAgIGp1c3RpZnktY29udGVudDogcmlnaHQ7DQogICAgbWFyZ2luOiAyNXB4IDAgNXB4IDA7DQogICAgYm94LXNpemluZzogYm9yZGVyLWJveDsNCg0KICAgIC50aXRsZXRleHQgew0KICAgICAgZm9udC1zaXplOiAyNnB4Ow0KICAgICAgZm9udC13ZWlnaHQ6IDMwMDsNCiAgICAgIGxldHRlci1zcGFjaW5nOiAxcHg7DQogICAgICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoDQogICAgICAgIDkyZGVnLA0KICAgICAgICAjMDA3MmZmIDAlLA0KICAgICAgICAjMDBlYWZmIDQ4Ljg1MjUzOTA2MjUlLA0KICAgICAgICAjMDFhYWZmIDEwMCUNCiAgICAgICk7DQogICAgICAtd2Via2l0LWJhY2tncm91bmQtY2xpcDogdGV4dDsNCiAgICAgIC13ZWJraXQtdGV4dC1maWxsLWNvbG9yOiB0cmFuc3BhcmVudDsNCiAgICAgIG1hcmdpbjogMCAxNnB4Ow0KICAgIH0NCg0KICAgIC56dW8sDQogICAgLnlvdSB7DQogICAgICBiYWNrZ3JvdW5kLXNpemU6IDEwMCUgMTAwJTsNCiAgICAgIHdpZHRoOiAyNnB4Ow0KICAgICAgaGVpZ2h0OiAxNnB4Ow0KICAgICAgbWFyZ2luLXRvcDogN3B4Ow0KICAgIH0NCg0KICAgIC56dW8gew0KICAgICAgYmFja2dyb3VuZDogdXJsKCIuLi8uLi9hc3NldHMvaW1nL3hpZXp1by5wbmciKSBuby1yZXBlYXQ7DQogICAgfQ0KDQogICAgLnlvdSB7DQogICAgICBiYWNrZ3JvdW5kOiB1cmwoIi4uLy4uL2Fzc2V0cy9pbWcveGlleW91LnBuZyIpIG5vLXJlcGVhdDsNCiAgICB9DQogIH0NCg0KICAubWFwd3JhcCB7DQogICAgaGVpZ2h0OiA5MDBweDsNCiAgICB3aWR0aDogMTAwJTsNCiAgICAvLyBwYWRkaW5nOiAwIDAgMTBweCAwOw0KICAgIGJveC1zaXppbmc6IGJvcmRlci1ib3g7DQogICAgcG9zaXRpb246IHJlbGF0aXZlOw0KDQogICAgLnF1YW5ndW8gew0KICAgICAgcG9zaXRpb246IGFic29sdXRlOw0KICAgICAgcmlnaHQ6IDIwcHg7DQogICAgICB0b3A6IC00NnB4Ow0KICAgICAgd2lkdGg6IDgwcHg7DQogICAgICBoZWlnaHQ6IDI4cHg7DQogICAgICBib3JkZXI6IDFweCBzb2xpZCAjMDBlZGVkOw0KICAgICAgYm9yZGVyLXJhZGl1czogMTBweDsNCiAgICAgIGNvbG9yOiAjMDBmN2Y2Ow0KICAgICAgdGV4dC1hbGlnbjogY2VudGVyOw0KICAgICAgbGluZS1oZWlnaHQ6IDI2cHg7DQogICAgICBsZXR0ZXItc3BhY2luZzogNnB4Ow0KICAgICAgY3Vyc29yOiBwb2ludGVyOw0KICAgICAgYm94LXNoYWRvdzogMCAycHggNHB4IHJnYmEoMCwgMjM3LCAyMzcsIDAuNSksDQogICAgICAgIDAgMCA2cHggcmdiYSgwLCAyMzcsIDIzNywgMC40KTsNCiAgICB9DQogIH0NCn0NCg=="}, {"version": 3, "sources": ["center-map.vue"], "names": [], "mappings": ";AA2TA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "center-map.vue", "sourceRoot": "src/views/indexs", "sourcesContent": ["<!--\r\n * @Author: daidai\r\n * @Date: 2022-03-01 11:17:39\r\n * @LastEditors: Please set LastEditors\r\n * @LastEditTime: 2022-09-29 15:50:18\r\n * @FilePath: \\web-pc\\src\\pages\\big-screen\\view\\indexs\\center-map.vue\r\n-->\r\n<template>\r\n  <div class=\"centermap\">\r\n    <div class=\"maptitle\">\r\n      <div class=\"zuo\"></div>\r\n      <span class=\"titletext\">{{ maptitle }}</span>\r\n      <div class=\"you\"></div>\r\n    </div>\r\n    <div class=\"mapwrap\">\r\n      <dv-border-box-13>\r\n        <div class=\"quanguo\" @click=\"getData('china')\" v-if=\"code !== 'china'\">\r\n          中国\r\n        </div>\r\n\r\n        <Echart id=\"CenterMap\" :options=\"options\" ref=\"CenterMap\" />\r\n      </dv-border-box-13>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport xzqCode from \"../../utils/map/xzqCode\";\r\nimport { currentGET } from \"api/modules\";\r\nimport * as echarts from \"echarts\";\r\nimport { GETNOBASE } from \"api\";\r\nimport { dataModule } from '@/utils/webSocket';\r\nexport default {\r\n  data() {\r\n    return {\r\n      maptitle: \"接入船总数量：\",\r\n      options: {},\r\n      code: \"china\", //china 代表中国 其他地市是行政编码\r\n      echartBindClick: false,\r\n      isSouthChinaSea: false, //是否要展示南海群岛  修改此值请刷新页面\r\n      wsCheckTimer: null, // WebSocket数据监听定时器\r\n      shipTotalCount: 0, // 船舶总数量\r\n      enterpriseTotalCount: 0, // 企业总数量\r\n    };\r\n  },\r\n  created() {},\r\n\r\n  mounted() {\r\n    // console.log(xzqCode);\r\n    this.getData(\"china\");\r\n    this.startDataMonitoring();\r\n  },\r\n  methods: {\r\n    getData(code) {\r\n      currentGET(\"big8\", { regionCode: code }).then((res) => {\r\n        console.log(\"设备分布\", res);\r\n        if (res.success) {\r\n          this.getGeojson(res.data.regionCode, res.data.dataList);\r\n          this.mapclick();\r\n        } else {\r\n          this.$Message.warning(res.msg);\r\n        }\r\n      });\r\n    },\r\n    /**\r\n     * @description: 获取geojson\r\n     * @param {*} name china 表示中国 其他省份行政区编码\r\n     * @param {*} mydata 接口返回列表数据\r\n     * @return {*}\r\n     */\r\n    async getGeojson(name, mydata) {\r\n      this.code = name;\r\n      //如果要展示南海群岛并且展示的是中国的话\r\n      let geoname=name\r\n      if (this.isSouthChinaSea && name == \"china\") {\r\n        geoname = \"chinaNanhai\";\r\n      }\r\n      //如果有注册地图的话就不用再注册 了\r\n      let mapjson = echarts.getMap(name);\r\n      if (mapjson) {\r\n        mapjson = mapjson.geoJSON;\r\n      } else {\r\n        mapjson = await GETNOBASE(`./map-geojson/${geoname}.json`).then((res) => {\r\n          return res;\r\n        });\r\n        echarts.registerMap(name, mapjson);\r\n      }\r\n      let cityCenter = {};\r\n      let arr = mapjson.features;\r\n      //根据geojson获取省份中心点\r\n      arr.map((item) => {\r\n        cityCenter[item.properties.name] =\r\n          item.properties.centroid || item.properties.center;\r\n      });\r\n      let newData = [];\r\n      mydata.map((item) => {\r\n        if (cityCenter[item.name]) {\r\n          newData.push({\r\n            name: item.name,\r\n            value: cityCenter[item.name].concat(item.value),\r\n          });\r\n        }\r\n      });\r\n      this.init(name, mydata, newData);\r\n    },\r\n    init(name, data, data2) {\r\n      // console.log(data2);\r\n      let top = 45;\r\n      let zoom = 1.05;\r\n      let option = {\r\n        backgroundColor: \"rgba(0,0,0,0)\",\r\n        tooltip: {\r\n          show: false,\r\n        },\r\n        legend: {\r\n          show: false,\r\n        },\r\n        visualMap: {\r\n          left: 20,\r\n          bottom: 20,\r\n          pieces: [\r\n            { gte: 1000, label: \"1000个以上\" }, // 不指定 max，表示 max 为无限大（Infinity）。\r\n            { gte: 600, lte: 999, label: \"600-999个\" },\r\n            { gte: 200, lte: 599, label: \"200-599个\" },\r\n            { gte: 50, lte: 199, label: \"49-199个\" },\r\n            { gte: 10, lte: 49, label: \"10-49个\" },\r\n            { lte: 9, label: \"1-9个\" }, // 不指定 min，表示 min 为无限大（-Infinity）。\r\n          ],\r\n          inRange: {\r\n            // 渐变颜色，从小到大\r\n            color: [\r\n              \"#c3d7df\",\r\n              \"#5cb3cc\",\r\n              \"#8abcd1\",\r\n              \"#66a9c9\",\r\n              \"#2f90b9\",\r\n              \"#1781b5\",\r\n            ],\r\n          },\r\n          textStyle: {\r\n            color: \"#fff\",\r\n          },\r\n        },\r\n        geo: {\r\n          map: name,\r\n          roam: false,\r\n          selectedMode: false, //是否允许选中多个区域\r\n          zoom: zoom,\r\n          top: top,\r\n          // aspectScale: 0.78,\r\n          show: false,\r\n        },\r\n        series: [\r\n          {\r\n            name: \"MAP\",\r\n            type: \"map\",\r\n            map: name,\r\n            // aspectScale: 0.78,\r\n            data: data,\r\n            // data: [1,100],\r\n            selectedMode: false, //是否允许选中多个区域\r\n            zoom: zoom,\r\n            geoIndex: 1,\r\n            top: top,\r\n            tooltip: {\r\n              show: true,\r\n              formatter: function (params) {\r\n                if (params.data) {\r\n                  return params.name + \"：\" + params.data[\"value\"];\r\n                } else {\r\n                  return params.name;\r\n                }\r\n              },\r\n              backgroundColor: \"rgba(0,0,0,.6)\",\r\n              borderColor: \"rgba(147, 235, 248, .8)\",\r\n              textStyle: {\r\n                color: \"#FFF\",\r\n              },\r\n            },\r\n            label: {\r\n              show: false,\r\n              color: \"#000\",\r\n              // position: [-10, 0],\r\n              formatter: function (val) {\r\n                // console.log(val)\r\n                if (val.data !== undefined) {\r\n                  return val.name.slice(0, 2);\r\n                } else {\r\n                  return \"\";\r\n                }\r\n              },\r\n              rich: {},\r\n            },\r\n            emphasis: {\r\n              label: {\r\n                show: false,\r\n              },\r\n              itemStyle: {\r\n                areaColor: \"#389BB7\",\r\n                borderWidth: 1,\r\n              },\r\n            },\r\n            itemStyle: {\r\n              borderColor: \"rgba(147, 235, 248, .8)\",\r\n              borderWidth: 1,\r\n              areaColor: {\r\n                type: \"radial\",\r\n                x: 0.5,\r\n                y: 0.5,\r\n                r: 0.8,\r\n                colorStops: [\r\n                  {\r\n                    offset: 0,\r\n                    color: \"rgba(147, 235, 248, 0)\", // 0% 处的颜色\r\n                  },\r\n                  {\r\n                    offset: 1,\r\n                    color: \"rgba(147, 235, 248, .2)\", // 100% 处的颜色\r\n                  },\r\n                ],\r\n                globalCoord: false, // 缺为 false\r\n              },\r\n              shadowColor: \"rgba(128, 217, 248, .3)\",\r\n              shadowOffsetX: -2,\r\n              shadowOffsetY: 2,\r\n              shadowBlur: 10,\r\n            },\r\n          },\r\n          {\r\n            data: data2,\r\n            type: \"effectScatter\",\r\n            coordinateSystem: \"geo\",\r\n            symbolSize: function (val) {\r\n              return 4;\r\n              // return val[2] / 50;\r\n            },\r\n            legendHoverLink: true,\r\n            showEffectOn: \"render\",\r\n            rippleEffect: {\r\n              // period: 4,\r\n              scale: 6,\r\n              color: \"rgba(255,255,255, 1)\",\r\n              brushType: \"fill\",\r\n            },\r\n            tooltip: {\r\n              show: true,\r\n              formatter: function (params) {\r\n                if (params.data) {\r\n                  return params.name + \"：\" + params.data[\"value\"][2];\r\n                } else {\r\n                  return params.name;\r\n                }\r\n              },\r\n              backgroundColor: \"rgba(0,0,0,.6)\",\r\n              borderColor: \"rgba(147, 235, 248, .8)\",\r\n              textStyle: {\r\n                color: \"#FFF\",\r\n              },\r\n            },\r\n            label: {\r\n              formatter: (param) => {\r\n                return param.name.slice(0, 2);\r\n              },\r\n\r\n              fontSize: 11,\r\n              offset: [0, 2],\r\n              position: \"bottom\",\r\n              textBorderColor: \"#fff\",\r\n              textShadowColor: \"#000\",\r\n              textShadowBlur: 10,\r\n              textBorderWidth: 0,\r\n              color: \"#FFF\",\r\n              show: true,\r\n            },\r\n            // colorBy: \"data\",\r\n            itemStyle: {\r\n              color: \"rgba(255,255,255,1)\",\r\n              borderColor: \"rgba(2255,255,255,2)\",\r\n              borderWidth: 4,\r\n              shadowColor: \"#000\",\r\n              shadowBlur: 10,\r\n            },\r\n          },\r\n        ],\r\n         //动画效果\r\n            // animationDuration: 1000,\r\n            // animationEasing: 'linear',\r\n            // animationDurationUpdate: 1000\r\n      };\r\n      this.options = option;\r\n    },\r\n    message(text) {\r\n      this.$Message({\r\n        text: text,\r\n        type: \"warning\",\r\n      });\r\n    },\r\n    mapclick() {\r\n      if (this.echartBindClick) return;\r\n      //单击切换到级地图，当mapCode有值,说明可以切换到下级地图\r\n      this.$refs.CenterMap.chart.on(\"click\", (params) => {\r\n        // console.log(params);\r\n        let xzqData = xzqCode[params.name];\r\n        if (xzqData) {\r\n          this.getData(xzqData.adcode);\r\n        } else {\r\n          this.message(\"暂无下级地市!\");\r\n        }\r\n      });\r\n      this.echartBindClick = true;\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.centermap {\r\n  .maptitle {\r\n    display: flex;\r\n    justify-content: right;\r\n    margin: 25px 0 5px 0;\r\n    box-sizing: border-box;\r\n\r\n    .titletext {\r\n      font-size: 26px;\r\n      font-weight: 300;\r\n      letter-spacing: 1px;\r\n      background: linear-gradient(\r\n        92deg,\r\n        #0072ff 0%,\r\n        #00eaff 48.8525390625%,\r\n        #01aaff 100%\r\n      );\r\n      -webkit-background-clip: text;\r\n      -webkit-text-fill-color: transparent;\r\n      margin: 0 16px;\r\n    }\r\n\r\n    .zuo,\r\n    .you {\r\n      background-size: 100% 100%;\r\n      width: 26px;\r\n      height: 16px;\r\n      margin-top: 7px;\r\n    }\r\n\r\n    .zuo {\r\n      background: url(\"../../assets/img/xiezuo.png\") no-repeat;\r\n    }\r\n\r\n    .you {\r\n      background: url(\"../../assets/img/xieyou.png\") no-repeat;\r\n    }\r\n  }\r\n\r\n  .mapwrap {\r\n    height: 900px;\r\n    width: 100%;\r\n    // padding: 0 0 10px 0;\r\n    box-sizing: border-box;\r\n    position: relative;\r\n\r\n    .quanguo {\r\n      position: absolute;\r\n      right: 20px;\r\n      top: -46px;\r\n      width: 80px;\r\n      height: 28px;\r\n      border: 1px solid #00eded;\r\n      border-radius: 10px;\r\n      color: #00f7f6;\r\n      text-align: center;\r\n      line-height: 26px;\r\n      letter-spacing: 6px;\r\n      cursor: pointer;\r\n      box-shadow: 0 2px 4px rgba(0, 237, 237, 0.5),\r\n        0 0 6px rgba(0, 237, 237, 0.4);\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}