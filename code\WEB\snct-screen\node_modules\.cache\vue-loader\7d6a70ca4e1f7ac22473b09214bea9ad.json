{"remainingRequest": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\src\\views\\indexs\\index.vue?vue&type=style&index=0&id=ce44a384&lang=scss&scoped=true&", "dependencies": [{"path": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\src\\views\\indexs\\index.vue", "mtime": 1753840651969}, {"path": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1753075296083}, {"path": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1753075298360}, {"path": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1753075296703}, {"path": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1753075295543}, {"path": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753075295560}, {"path": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753075297856}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQovLyDlhoXlrrkNCi5jb250ZW50cyB7DQogIC5jb250ZXRuX2xlZnQsDQogIC5jb250ZXRuX3JpZ2h0IHsNCiAgICB3aWR0aDogNDMwcHg7DQogICAgYm94LXNpemluZzogYm9yZGVyLWJveDsNCiAgfQ0KDQogIC5jb250ZXRuX2xlZnQgew0KICAgIGhlaWdodDogOTYwcHg7DQogICAgZ2FwOiAxMHB4Ow0KICB9DQoNCiAgLmNvbnRldG5fY2VudGVyIHsNCiAgICBoZWlnaHQ6IDk2MHB4Ow0KICAgIHdpZHRoOiAxNDM5cHg7DQogIH0NCg0KICAvL+W3puWPs+S4pOS+pyDkuInkuKrlnZcNCiAgLmNvbnRldG5fbHItaXRlbSB7DQogICAgaGVpZ2h0OiAzMTBweDsNCiAgfQ0KDQogIC8vIOS8geS4muWIl+ihqOWMuuWfnw0KICAuY29udGV0bl9sci1pdGVtLWxhcmdlIHsNCiAgICBoZWlnaHQ6IDYzNXB4Ow0KICB9DQoNCiAgLmNvbnRldG5fY2VudGVyX3RvcCB7DQogICAgd2lkdGg6IDEwMCU7DQogIH0NCg0KICAvLyDkuK3pl7QNCiAgLmNvbnRldG5fY2VudGVyIHsNCiAgICBkaXNwbGF5OiBmbGV4Ow0KICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47DQogICAganVzdGlmeS1jb250ZW50OiBzcGFjZS1hcm91bmQ7DQogIH0NCg0KICAuY29udGV0bl9jZW50ZXItYm90dG9tIHsNCiAgICBoZWlnaHQ6IDMxNXB4Ow0KICB9DQoNCiAgLy/lt6bovrkg5Y+z6L65IOe7k+aehOS4gOagtw0KICAuY29udGV0bl9sZWZ0LA0KICAuY29udGV0bl9yaWdodCB7DQogICAgZGlzcGxheTogZmxleDsNCiAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOw0KICAgIGp1c3RpZnktY29udGVudDogc3BhY2UtYXJvdW5kOw0KICAgIHBvc2l0aW9uOiByZWxhdGl2ZTsNCiAgfQ0KfQ0KDQoNCkBrZXlmcmFtZXMgcm90YXRpbmcgew0KICAgIDAlIHsNCiAgICAgICAgLXdlYmtpdC10cmFuc2Zvcm06IHJvdGF0ZSgwKSBzY2FsZSgxKTsNCiAgICAgICAgdHJhbnNmb3JtOiByb3RhdGUoMCkgc2NhbGUoMSk7DQogICAgfQ0KICAgIDUwJSB7DQogICAgICAgIC13ZWJraXQtdHJhbnNmb3JtOiByb3RhdGUoMTgwZGVnKSBzY2FsZSgxLjEpOw0KICAgICAgICB0cmFuc2Zvcm06IHJvdGF0ZSgxODBkZWcpIHNjYWxlKDEuMSk7DQogICAgfQ0KICAgIDEwMCUgew0KICAgICAgICAtd2Via2l0LXRyYW5zZm9ybTogcm90YXRlKDM2MGRlZykgc2NhbGUoMSk7DQogICAgICAgIHRyYW5zZm9ybTogcm90YXRlKDM2MGRlZykgc2NhbGUoMSk7DQogICAgfQ0KfQ0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AA6HA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/indexs", "sourcesContent": ["<!--\r\n * @Author: daidai\r\n * @Date: 2022-03-04 09:23:59\r\n * @LastEditors: Please set LastEditors\r\n * @LastEditTime: 2022-05-07 11:05:02\r\n * @FilePath: \\web-pc\\src\\pages\\big-screen\\view\\indexs\\index.vue\r\n-->\r\n<template>\r\n  <div class=\"contents\">\r\n    <div class=\"contetn_left\">\r\n      <div class=\"pagetab\">\r\n        <!-- <div class=\"item\">实时监测</div> -->\r\n        \r\n      </div>\r\n      <ItemWrap class=\"contetn_left-company contetn_lr-item-large\" title=\"企业列表\">\r\n        <LeftCompanyList />\r\n      </ItemWrap>\r\n      <ItemWrap\r\n        class=\"contetn_left-bottom contetn_lr-item\"\r\n        title=\"设备提醒\"\r\n        style=\"padding: 0 10px 16px 10px\"\r\n      >\r\n        <LeftBottom />\r\n      </ItemWrap>\r\n    </div>\r\n    <div class=\"contetn_center\">\r\n      <CenterMap class=\"contetn_center_top\" />\r\n      <!--\r\n      <ItemWrap class=\"contetn_center-bottom\" title=\"安装计划\">\r\n        <CenterBottom />\r\n      </ItemWrap>\r\n      -->\r\n    </div>\r\n    <!--\r\n    <div class=\"contetn_right\">\r\n      <ItemWrap\r\n        class=\"contetn_left-bottom contetn_lr-item\"\r\n        title=\"报警次数\"\r\n      >\r\n        <RightTop />\r\n      </ItemWrap>\r\n      <ItemWrap\r\n        class=\"contetn_left-bottom contetn_lr-item\"\r\n        title=\"报警排名(TOP8)\"\r\n        style=\"padding: 0 10px 16px 10px\"\r\n      >\r\n        <RightCenter />\r\n      </ItemWrap>\r\n      \r\n      <ItemWrap\r\n        class=\"contetn_left-bottom contetn_lr-item\"\r\n        title=\"数据统计图 \"\r\n      >\r\n        <RightBottom />\r\n      </ItemWrap>\r\n    </div>\r\n    -->\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport LeftCompanyList from './left-company-list.vue'\r\nimport LeftBottom from \"./left-bottom.vue\";\r\nimport CenterMap from \"./center-map.vue\";\r\nimport CenterBottom from \"./center-bottom.vue\";\r\nimport RightTop from \"./right-top.vue\";\r\nimport RightCenter from \"./right-center.vue\";\r\nimport RightBottom from \"./right-bottom.vue\";\r\nimport { initWebSocket, dataModule, manualClose, clearData } from '@/utils/webSocket'\r\n\r\nexport default {\r\n  components: {\r\n    LeftCompanyList,\r\n    LeftBottom,\r\n    CenterMap,\r\n    RightTop,\r\n    RightCenter,\r\n    RightBottom,\r\n    CenterBottom,\r\n  },\r\n  data() {\r\n    return {\r\n    \r\n    };\r\n  },\r\n  filters: {\r\n    numsFilter(msg) {\r\n      return msg || 0;\r\n    },\r\n  },\r\n  created() {\r\n    \r\n  },\r\n\r\n  mounted() {\r\n    // 统一初始化WebSocket连接，管理整个页面的数据获取\r\n    this.initWebSocketConnection()\r\n  },\r\n  methods: {\r\n    // 统一初始化WebSocket连接\r\n    initWebSocketConnection() {\r\n      try {\r\n        // 初始化WebSocket连接\r\n        manualClose()\r\n        clearData()\r\n\r\n        // 设置请求所有需要的数据编号，通过逗号分隔\r\n        dataModule.sendtext = \"type66#100#0#0A01,0A02,0A03,0A04\"\r\n\r\n        initWebSocket()\r\n        console.log('主页面WebSocket连接已初始化，请求数据编号:', dataModule.sendtext)\r\n      } catch (error) {\r\n        console.error('主页面WebSocket连接失败:', error)\r\n      }\r\n    },\r\n\r\n    // // 处理子组件的重连请求\r\n    // handleWebSocketReconnect() {\r\n    //   console.log('收到子组件重连请求，重新初始化WebSocket连接')\r\n    //   this.initWebSocketConnection()\r\n    // },\r\n  },\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n// 内容\r\n.contents {\r\n  .contetn_left,\r\n  .contetn_right {\r\n    width: 430px;\r\n    box-sizing: border-box;\r\n  }\r\n\r\n  .contetn_left {\r\n    height: 960px;\r\n    gap: 10px;\r\n  }\r\n\r\n  .contetn_center {\r\n    height: 960px;\r\n    width: 1439px;\r\n  }\r\n\r\n  //左右两侧 三个块\r\n  .contetn_lr-item {\r\n    height: 310px;\r\n  }\r\n\r\n  // 企业列表区域\r\n  .contetn_lr-item-large {\r\n    height: 635px;\r\n  }\r\n\r\n  .contetn_center_top {\r\n    width: 100%;\r\n  }\r\n\r\n  // 中间\r\n  .contetn_center {\r\n    display: flex;\r\n    flex-direction: column;\r\n    justify-content: space-around;\r\n  }\r\n\r\n  .contetn_center-bottom {\r\n    height: 315px;\r\n  }\r\n\r\n  //左边 右边 结构一样\r\n  .contetn_left,\r\n  .contetn_right {\r\n    display: flex;\r\n    flex-direction: column;\r\n    justify-content: space-around;\r\n    position: relative;\r\n  }\r\n}\r\n\r\n\r\n@keyframes rotating {\r\n    0% {\r\n        -webkit-transform: rotate(0) scale(1);\r\n        transform: rotate(0) scale(1);\r\n    }\r\n    50% {\r\n        -webkit-transform: rotate(180deg) scale(1.1);\r\n        transform: rotate(180deg) scale(1.1);\r\n    }\r\n    100% {\r\n        -webkit-transform: rotate(360deg) scale(1);\r\n        transform: rotate(360deg) scale(1);\r\n    }\r\n}\r\n</style>\r\n"]}]}